import { atomWithStorage } from "jotai/utils";
import { LayoutGrid } from "lucide-react";
import { IItemSidebar, ISubItemSidebar } from "../types/sidebar-active-item.type";

const defaultItem: IItemSidebar = {
	label: "Dashboard",
	id: "dashboard",
	Icon: LayoutGrid,
	accessibleDescription: "ícone da dashboard",
	path: "/dashboard",
	type: "menu",
	moduleActivated: true,
};

export const itemSidebarActiveAtom = atomWithStorage<IItemSidebar>("item-sidebar-active", defaultItem);

export const subItemSidebarActiveAtom = atomWithStorage<ISubItemSidebar | null>("sub-item-sidebar-active", null);
