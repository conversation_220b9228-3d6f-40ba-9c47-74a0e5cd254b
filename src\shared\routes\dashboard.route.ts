import { DashboardLayout } from "@/layout/components/layout/desktop/dashboard-layout";
import { DashboardPage } from "@/modules/dashboard/pages/dashboard";
import { createRoute } from "@tanstack/react-router";
import { rootRoute } from "./config";

export const home = createRoute({
	getParentRoute: () => rootRoute,
	path: "/",
	component: DashboardLayout,
});

export const dashboardIndexRoute = createRoute({
	getParentRoute: () => home,
	path: "/",
	id: "dashboard-index",
	component: DashboardPage,
});
