import { MainContainer } from "@/layout/components/layout/main-container";
import { dashboardRoutes } from "@/modules/dashboard/routes/routes";
import { financialRoutes } from "@/modules/financial/routes/routes";
import { ordersRoutes } from "@/modules/orders/routes/routes";
import { personRoutes } from "@/modules/person/routes/routes";
import { productRoutes } from "@/modules/product/routes/routes";
import { salesPanelRoutes } from "@/modules/sales-panel/routes/routes";
import { stockRoutes } from "@/modules/stock/routes/routes";
import { createRootRoute } from "@tanstack/react-router";
import { dashboardIndexRoute, home } from "./dashboard.route";

export const rootRoute = createRootRoute({
	component: MainContainer,
});

export const routeTree = rootRoute.addChildren([
	home.addChildren([
		dashboardIndexRoute,
		...dashboardRoutes,
		...productRoutes,
		...stockRoutes,
		...financialRoutes,
		...personRoutes,
		...ordersRoutes,
	]),
	...salesPanelRoutes,
]);
