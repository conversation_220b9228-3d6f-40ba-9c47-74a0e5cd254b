import { sidebarTransitions } from "@/layout/data/constants/sidebar-transitions";
import { SidebarItems } from "@/layout/hooks/sidebar/get-items.hook";
import { useItemsSidebar } from "@/layout/hooks/sidebar/items-sidebar.hook";
import { useSidebarMobile } from "@/layout/hooks/sidebar/sidebar-mobile.hook";
import { IItemSidebar, ISubItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { MobileBottomBar } from "./footer-siderbar";
import { MenuItemGrid } from "./menu-item-grid";
import { MenuOverlay } from "./mobile-overlay";

const getItems = new SidebarItems();

export function SidebarMobile() {
	const { isMenuVisible, toggleMenu, closeMenu } = useSidebarMobile();
	const { itemActive, handleItemActive, menuItems, configItems, otherItems } = useItemsSidebar(getItems);

	const [selectedSubItems, setSelectedSubItems] = useState<IItemSidebar | ISubItemSidebar | null>(null);

	const handleItemClick = (item: IItemSidebar | ISubItemSidebar) => {
		if (item.subItems && item.subItems.length > 0) {
			setSelectedSubItems(item);
			return;
		}
		handleItemActive(item as IItemSidebar);
		closeMenu();
		setSelectedSubItems(null);
	};

	const handleBack = () => {
		setSelectedSubItems(null);
	};

	return (
		<>
			<MobileBottomBar onToggleMenu={toggleMenu} getItems={getItems} />
			<MenuOverlay isVisible={isMenuVisible} onClose={closeMenu}>
				<div className="w-full max-w-md h-[40vh] overflow-hidden">
					<AnimatePresence mode="wait">
						{selectedSubItems ? (
							<motion.div
								key="subItemsView"
								variants={sidebarTransitions}
								initial="enter"
								animate="center"
								exit="exit"
								className="w-full h-full flex flex-col"
							>
								<div className="flex items-center mb-4">
									<div onClick={handleBack} className="p-2 cursor-pointer">
										<ArrowLeft size={20} />
									</div>
									<span>{selectedSubItems.label}</span>
								</div>
								<div className="grid grid-cols-2 sm:grid-cols-3 gap-4 overflow-y-auto">
									<MenuItemGrid items={selectedSubItems.subItems || []} itemActive={itemActive} onItemClick={handleItemClick} />
								</div>
							</motion.div>
						) : (
							<motion.div
								key="mainItemsView"
								variants={sidebarTransitions}
								initial="enter"
								animate="center"
								exit="exit"
								className="w-full h-full"
							>
								<div className="grid grid-cols-2 sm:grid-cols-3 gap-4 overflow-y-auto">
									<MenuItemGrid items={menuItems} itemActive={itemActive} onItemClick={handleItemClick} />
									<MenuItemGrid items={configItems} itemActive={itemActive} onItemClick={handleItemClick} />
									<MenuItemGrid
										items={otherItems}
										itemActive={itemActive}
										onItemClick={handleItemClick}
										activeClassName="bg-mainColor text-white"
										inactiveClassName="bg-gray-100 hover:bg-gray-200 text-gray-600"
									/>
								</div>
							</motion.div>
						)}
					</AnimatePresence>
				</div>
			</MenuOverlay>
		</>
	);
}
