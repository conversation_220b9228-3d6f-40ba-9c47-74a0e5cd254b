import { AnyRoute, createRoute, RouteComponent } from "@tanstack/react-router";

export interface ModuleRouteConfig<T extends { id: string; path: string }> {
	moduleActivated: boolean;
	subItems?: T[];
}

export interface ModuleSubRoute<T extends { id: string; path: string }, P = object> {
	id: T["id"];
	component: RouteComponent<P>;
}

export function createModuleRoutes<T extends { id: string; path: string }, P = object>(
	moduleConfig: ModuleRouteConfig<T>,
	subRoutes: ModuleSubRoute<T, P>[],
	getParentRoute: () => AnyRoute
): ReturnType<typeof createRoute>[] {
	if (!moduleConfig.moduleActivated) {
		return [];
	}

	if (!moduleConfig.subItems || moduleConfig.subItems.length === 0) {
		return [
			createRoute({
				component: subRoutes[0]?.component,
				path: moduleConfig.subItems?.[0]?.path || "",
				getParentRoute,
			}),
		];
	}

	return subRoutes.map(routeDef => {
		const config = moduleConfig.subItems!.find(item => item.id === routeDef.id);
		return createRoute({
			component: routeDef.component,
			path: config?.path ?? "",
			getParentRoute,
		});
	});
}
