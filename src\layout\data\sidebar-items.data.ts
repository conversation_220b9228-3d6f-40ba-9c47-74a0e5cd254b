import { FINANCIAL_CONFIG } from "@/modules/financial/data/financial-config";
import { ORDERS_CONFIG } from "@/modules/orders/data/orders-config";
import { PERSON_CONFIG } from "@/modules/person/data/person-config";
import { PRODUCT_CONFIG } from "@/modules/product/data/product-config";
import { SALES_PANEL_CONFIG } from "@/modules/sales-panel/data/sales-panel-config";
import { STOCK_CONFIG } from "@/modules/stock/data/stock-config";
import { LayoutGrid, Settings } from "lucide-react";
import { IItemSidebar } from "../types/sidebar-active-item.type";

export const SIDEBAR_ITEMS: IItemSidebar[] = [
	{
		label: "Dashboard",
		id: "dashboard",
		moduleActivated: true,
		Icon: LayoutGrid,
		accessibleDescription: "ícone da dashboard",
		path: "/",
		type: "menu",
	},
	...[PRODUCT_CONFIG],
	...[STOCK_CONFIG],
	...[SALES_PANEL_CONFIG],
	...[FINANCIAL_CONFIG],
	...[PERSON_CONFIG],
	...[ORDERS_CONFIG],
	// {
	// 	label: "Estoque",
	// 	id: "stock",
	// 	Icon: Archive,
	// 	moduleActivated: true,
	// 	accessibleDescription: "ícone representativo do estoque",
	// 	path: "/estoque",
	// 	type: "menu",
	// 	subItems: [
	// 		{
	// 			label: "Visão Geral",
	// 			id: "stock-overview",
	// 			Icon: Layers2,
	// 			accessibleDescription: "ícone de visão geral do estoque",
	// 			path: "/estoque",
	// 		},
	// 		{
	// 			label: "Entradas e Saídas",
	// 			id: "stock-movements",
	// 			Icon: ArrowUpDown,
	// 			accessibleDescription: "ícone de entradas e saídas",
	// 			path: "/estoque/entradas-saidas",
	// 		},
	// 		{
	// 			label: "Adicionar XML",
	// 			id: "stock-add-xml",
	// 			Icon: FilePlus,
	// 			accessibleDescription: "ícone de adicionar XML",
	// 			path: "/estoque/adicionar-xml",
	// 		},
	// 	],
	// },
	// {
	// 	label: "Pedidos",
	// 	id: "orders",
	// 	moduleActivated: true,
	// 	Icon: ShoppingCart,
	// 	accessibleDescription: "ícone pedidos",
	// 	path: "/pedidos",
	// 	type: "menu",
	// 	subItems: [],
	// },
	// {
	// 	label: "Clientes",
	// 	id: "customers",
	// 	moduleActivated: true,
	// 	Icon: Users,
	// 	accessibleDescription: "ícone da seção de clientes",
	// 	path: "/clientes",
	// 	type: "menu",
	// 	subItems: [],
	// },
	// {
	// 	label: "Gráficos",
	// 	id: "charts",
	// 	moduleActivated: true,
	// 	Icon: ChartNoAxesCombined,
	// 	accessibleDescription: "ícone da seção de gráficos",
	// 	path: "/graficos",
	// 	type: "menu",
	// 	subItems: [],
	// },
	// {
	// 	label: "Notificações",
	// 	id: "notifications",
	// 	Icon: BellRing,
	// 	moduleActivated: true,
	// 	accessibleDescription: "ícone da seção de notificações",
	// 	path: "/notificacoes",
	// 	type: "menu",
	// 	subItems: [],
	// },
	// {
	// 	label: "Finanças",
	// 	id: "finances",
	// 	moduleActivated: true,
	// 	Icon: CircleDollarSign,
	// 	accessibleDescription: "ícone da seção de finanças",
	// 	path: "/financas",
	// 	type: "menu",
	// 	subItems: [],
	// },
	{
		label: "Configurações",
		id: "settings",
		Icon: Settings,
		moduleActivated: true,
		accessibleDescription: "ícone de configurações",
		path: "/configuracoes",
		type: "config",
		subItems: [],
	},
] as const;
