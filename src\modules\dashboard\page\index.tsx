import { useLocation } from "@tanstack/react-router";
import { BarChart2, Calendar, Download, FileText, Filter, RefreshCw, TrendingUp } from "lucide-react";
import { useEffect, useState } from "react";
import { Bar, BarChart, CartesianGrid, Legend, Line, LineChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

const vendasMensais = [
	{ name: "Jan", valor: 4000 },
	{ name: "Fev", valor: 3000 },
	{ name: "<PERSON>", valor: 5000 },
	{ name: "Abr", valor: 2780 },
	{ name: "<PERSON>", valor: 6890 },
];

const produtosMaisVendidos = [
	{ name: "Detergente", value: 400 },
	{ name: "Desinfetante", value: 300 },
	{ name: "Sabão em pó", value: 250 },
	{ name: "<PERSON><PERSON><PERSON><PERSON>", value: 200 },
	{ name: "<PERSON>ac<PERSON><PERSON>", value: 150 },
];

const clientesPorRegiao = [
	{ name: "<PERSON><PERSON>", valor: 24 },
	{ name: "<PERSON><PERSON><PERSON>", valor: 40 },
	{ name: "<PERSON> José", valor: 15 },
	{ name: "Bela Vista", valor: 18 },
	{ name: "Monte Verde", valor: 10 },
];

const relatoriosPredefinidos = [
	{ id: 1, nome: "Vendas Mensais", descricao: "Relatório detalhado de vendas por mês" },
	{ id: 2, nome: "Catálogo de produtos", descricao: "Listagem completa de produtos disponíveis com especificações" },
	{ id: 3, nome: "Estoque Atual", descricao: "Situação atual do estoque com alertas" },
	{ id: 4, nome: "Clientes por Bairro", descricao: "Distribuição geográfica dos clientes" },
	{ id: 5, nome: "Produtos mais Vendidos", descricao: "Produtos com maior saída" },
];

const indicadores = [
	{ id: 1, titulo: "Faturamento", valor: "R$ 152.350,00", percentual: "+15%", positivo: true },
	{ id: 2, titulo: "Novos Clientes", valor: "48", percentual: "+22%", positivo: true },
	{ id: 3, titulo: "Produtos Vendidos", valor: "1.243", percentual: "+5%", positivo: true },
	{ id: 4, titulo: "Tickets Médio", valor: "R$ 89,50", percentual: "-3%", positivo: false },
];

const MAIN_CONTENT_ID = "main-scrollable-area";

export default function DashboardLayout() {
	const location = useLocation();

	useEffect(() => {
		const mainContent = document.getElementById(MAIN_CONTENT_ID);
		mainContent?.scrollTo({
			top: 0,
			behavior: "instant",
		});
	}, [location.pathname]);

	const [periodoSelecionado, setPeriodoSelecionado] = useState("mensal");
	const [relatorioAtivo, setRelatorioAtivo] = useState<number | null>(null);

	const gerarPDF = (relatorioId: number) => {
		const relatorio = relatoriosPredefinidos.find(r => r.id === relatorioId);
		if (relatorio) {
			alert(`Gerando PDF do relatório: ${relatorio.nome}`);
		} else {
			alert("Relatório não encontrado");
		}
	};

	const abrirRelatorio = (relatorioId: number) => {
		setRelatorioAtivo(relatorioId);
	};

	const fecharRelatorio = () => {
		setRelatorioAtivo(null);
	};

	return (
		<div className="flex flex-col w-full h-full bg-gray-50">
			<div className="flex items-center justify-between p-4 bg-white shadow-sm">
				<div className="flex items-center space-x-4">
					<div className="flex items-center">
						<Calendar className="w-5 h-5 text-gray-500 mr-2" />
						<select className="border rounded p-1 text-sm">
							<option>Último mês</option>
							<option>Últimos 3 meses</option>
							<option>Último ano</option>
							<option>Personalizado</option>
						</select>
					</div>

					<div className="flex items-center">
						<Filter className="w-5 h-5 text-gray-500 mr-2" />
						<select className="border rounded p-1 text-sm">
							<option>Todos os produtos</option>
							<option>Limpeza doméstica</option>
							<option>Limpeza industrial</option>
						</select>
					</div>
				</div>

				<button className="flex items-center bg-blue-50 text-blue-600 px-3 py-1 rounded text-sm">
					<RefreshCw className="w-4 h-4 mr-1" />
					Atualizar dados
				</button>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
				{indicadores.map(indicador => (
					<div key={indicador.id} className="bg-white p-4 rounded-lg shadow-sm">
						<div className="text-sm text-gray-500">{indicador.titulo}</div>
						<div className="text-2xl font-semibold mt-1">{indicador.valor}</div>
						<div className={`text-sm mt-2 ${indicador.positivo ? "text-green-600" : "text-red-600"}`}>
							{indicador.percentual} em relação ao período anterior
						</div>
					</div>
				))}
			</div>

			<div className="flex border-b px-4">
				<button
					className={`px-4 py-2 font-medium text-sm ${periodoSelecionado === "diario" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500"}`}
					onClick={() => setPeriodoSelecionado("diario")}
				>
					Diário
				</button>
				<button
					className={`px-4 py-2 font-medium text-sm ${periodoSelecionado === "semanal" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500"}`}
					onClick={() => setPeriodoSelecionado("semanal")}
				>
					Semanal
				</button>
				<button
					className={`px-4 py-2 font-medium text-sm ${periodoSelecionado === "mensal" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500"}`}
					onClick={() => setPeriodoSelecionado("mensal")}
				>
					Mensal
				</button>
				<button
					className={`px-4 py-2 font-medium text-sm ${periodoSelecionado === "anual" ? "border-b-2 border-blue-500 text-blue-600" : "text-gray-500"}`}
					onClick={() => setPeriodoSelecionado("anual")}
				>
					Anual
				</button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4">
				<div className="bg-white p-4 rounded-lg shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<h3 className="font-medium">Vendas Mensais</h3>
						<div className="flex items-center text-sm text-blue-600">
							<BarChart2 className="w-4 h-4 mr-1" />
							Ver detalhes
						</div>
					</div>
					<ResponsiveContainer width="100%" height={250}>
						<BarChart data={vendasMensais}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="name" />
							<YAxis />
							<Tooltip />
							<Bar dataKey="valor" fill="#3B82F6" />
						</BarChart>
					</ResponsiveContainer>
				</div>

				<div className="bg-white p-4 rounded-lg shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<h3 className="font-medium">Produtos Mais Vendidos</h3>
						<div className="flex items-center text-sm text-blue-600">
							<BarChart2 className="w-4 h-4 mr-1" />
							Ver detalhes
						</div>
					</div>
					<ResponsiveContainer width="100%" height={250}>
						<PieChart>
							<Pie
								data={produtosMaisVendidos}
								cx="50%"
								cy="50%"
								outerRadius={80}
								fill="#8884d8"
								dataKey="value"
								nameKey="name"
								label
							></Pie>
							<Tooltip />
							<Legend />
						</PieChart>
					</ResponsiveContainer>
				</div>

				<div className="bg-white p-4 rounded-lg shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<h3 className="font-medium">Clientes por Bairro</h3>
						<div className="flex items-center text-sm text-blue-600">
							<TrendingUp className="w-4 h-4 mr-1" />
							Ver detalhes
						</div>
					</div>
					<ResponsiveContainer width="100%" height={250}>
						<LineChart data={clientesPorRegiao}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="name" />
							<YAxis />
							<Tooltip />
							<Line type="monotone" dataKey="valor" stroke="#10B981" strokeWidth={2} />
						</LineChart>
					</ResponsiveContainer>
				</div>

				<div className="bg-white p-4 rounded-lg shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<h3 className="font-medium">Relatórios</h3>
						<button className="text-sm text-blue-600 flex items-center">
							<FileText className="w-4 h-4 mr-1" />
							Todos os relatórios
						</button>
					</div>
					<div className="space-y-2">
						{relatoriosPredefinidos.map(relatorio => (
							<div key={relatorio.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
								<div>
									<div className="font-medium text-sm">{relatorio.nome}</div>
									<div className="text-xs text-gray-500">{relatorio.descricao}</div>
								</div>
								<div className="flex space-x-2">
									<button className="p-1 text-gray-500 hover:text-blue-600" onClick={() => abrirRelatorio(relatorio.id)}>
										<FileText className="w-4 h-4" />
									</button>
									<button className="p-1 text-gray-500 hover:text-blue-600" onClick={() => gerarPDF(relatorio.id)}>
										<Download className="w-4 h-4" />
									</button>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{relatorioAtivo && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg max-w-4xl w-full max-h-full overflow-auto">
						<div className="flex justify-between items-center p-4 border-b">
							<h3 className="font-medium">{relatoriosPredefinidos.find(r => r.id === relatorioAtivo)?.nome || "Relatório"}</h3>
							<div className="flex items-center space-x-2">
								<button
									className="bg-blue-600 text-white px-3 py-1 rounded text-sm flex items-center"
									onClick={() => gerarPDF(relatorioAtivo)}
								>
									<Download className="w-4 h-4 mr-1" />
									Exportar PDF
								</button>
								<button className="bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm" onClick={fecharRelatorio}>
									Fechar
								</button>
							</div>
						</div>
						<div className="p-4">
							<div className="space-y-4">
								<p className="text-sm text-gray-600">
									Conteúdo detalhado do relatório "
									{relatoriosPredefinidos.find(r => r.id === relatorioAtivo)?.nome || "Desconhecido"}". Aqui seriam exibidos os
									dados completos do relatório com tabelas, gráficos e análises detalhadas.
								</p>
								<div className="border rounded p-4">
									<ResponsiveContainer width="100%" height={300}>
										{relatorioAtivo === 1 ? (
											<BarChart data={vendasMensais}>
												<CartesianGrid strokeDasharray="3 3" />
												<XAxis dataKey="name" />
												<YAxis />
												<Tooltip />
												<Legend />
												<Bar dataKey="valor" name="Valor (R$)" fill="#3B82F6" />
											</BarChart>
										) : relatorioAtivo === 2 ? (
											<PieChart>
												<Pie
													data={produtosMaisVendidos}
													cx="50%"
													cy="50%"
													outerRadius={100}
													fill="#8884d8"
													dataKey="value"
													nameKey="name"
													label
												/>
												<Tooltip />
												<Legend />
											</PieChart>
										) : (
											<LineChart data={clientesPorRegiao}>
												<CartesianGrid strokeDasharray="3 3" />
												<XAxis dataKey="name" />
												<YAxis />
												<Tooltip />
												<Legend />
												<Line type="monotone" dataKey="valor" name="Quantidade" stroke="#10B981" strokeWidth={2} />
											</LineChart>
										)}
									</ResponsiveContainer>
								</div>

								<div className="border rounded overflow-hidden">
									<table className="min-w-full divide-y divide-gray-200">
										<thead className="bg-gray-50">
											<tr>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Item
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Quantidade
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Valor
												</th>
												<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
													Status
												</th>
											</tr>
										</thead>
										<tbody className="bg-white divide-y divide-gray-200">
											{[1, 2, 3, 4, 5].map(item => (
												<tr key={item}>
													<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Item {item}</td>
													<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
														{Math.floor(Math.random() * 100)}
													</td>
													<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
														R$ {(Math.random() * 1000).toFixed(2)}
													</td>
													<td className="px-6 py-4 whitespace-nowrap">
														<span
															className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
																item % 3 === 0
																	? "bg-red-100 text-red-800"
																	: item % 2 === 0
																		? "bg-green-100 text-green-800"
																		: "bg-yellow-100 text-yellow-800"
															}`}
														>
															{item % 3 === 0 ? "Crítico" : item % 2 === 0 ? "Normal" : "Atenção"}
														</span>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
