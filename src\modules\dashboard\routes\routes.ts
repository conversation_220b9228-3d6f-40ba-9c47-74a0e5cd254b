import { home } from "@/shared/routes/dashboard.route";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import { DASHBOARD_CONFIG, DASHBOARD_SUBITEM_IDS } from "../data/dashboard-config";
import { DashboardPage } from "../pages/dashboard";

const dashboardSubRoutes: ModuleSubRoute<NonNullable<typeof DASHBOARD_CONFIG.subItems>[number]>[] = [
	{ id: DASHBOARD_SUBITEM_IDS.DASHBOARD, component: DashboardPage },
];

export const dashboardRoutes = createModuleRoutes(
	{
		...DASHBOARD_CONFIG,
		subItems: DASHBOARD_CONFIG.subItems ?? [],
	},
	dashboardSubRoutes,
	() => home
);
